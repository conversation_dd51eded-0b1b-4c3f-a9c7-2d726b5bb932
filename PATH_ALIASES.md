# Path Aliases Configuration

Этот проект настроен с алиасами путей для упрощения импортов и избежания длинных относительных путей типа `../../../`.

## Настроенные алиасы

| Алиас | Путь | Описание |
|-------|------|----------|
| `@/*` | `src/*` | Корневая папка src |
| `@/components` | `src/components` | Компоненты |
| `@/pages` | `src/pages` | Страницы |
| `@/modules` | `src/modules` | Модули |
| `@/utils` | `src/utils` | Утилиты и константы |
| `@/hooks` | `src/hooks` | React хуки |
| `@/store` | `src/store` | Redux store |
| `@/types` | `src/types` | TypeScript типы |
| `@/assets` | `src/assets` | Статические ресурсы |
| `@/styles` | `src/styles` | Стили |
| `@/api` | `src/api` | API функции |
| `@/contexts` | `src/contexts` | React контексты |
| `@/app` | `src/app` | Конфигурация приложения |

## Примеры использования

### До (с относительными путями):
```typescript
// В файле src/components/Header/index.tsx
import { useResponsive } from '../../hooks';
import { HEADER_MENU_ITEMS } from '../../utils/constants';
import { AppButton } from '../../components/AppButton';
import AppLogo from '../../assets/images/Logo.png';
import { type RootState } from '../../store';
```

### После (с алиасами):
```typescript
// В файле src/components/Header/index.tsx
import { useResponsive } from '@/hooks';
import { HEADER_MENU_ITEMS } from '@/utils/constants';
import { AppButton } from '@/components';
import AppLogo from '@/assets/images/Logo.png';
import { type RootState } from '@/store';
```

### В SCSS файлах:
```scss
// До
@use '../../styles/media' as *;

// После
@use '@/styles/media' as *;
```

## Преимущества

1. **Читаемость**: Импорты становятся более понятными
2. **Поддержка**: Легче рефакторить и перемещать файлы
3. **Консистентность**: Единообразные пути во всем проекте
4. **Автодополнение**: IDE лучше работает с абсолютными путями

## Конфигурация

Алиасы настроены в следующих файлах:

### TypeScript (`tsconfig.app.json`)
```json
{
  "compilerOptions": {
    "baseUrl": ".",
    "paths": {
      "@/*": ["src/*"],
      "@/components": ["src/components"],
      "@/utils": ["src/utils"],
      // ... другие алиасы
    }
  }
}
```

### Vite (`vite.config.ts`)
```typescript
export default defineConfig({
  resolve: {
    alias: {
      '@': path.resolve(__dirname, './src'),
      '@/components': path.resolve(__dirname, './src/components'),
      '@/utils': path.resolve(__dirname, './src/utils'),
      // ... другие алиасы
    },
  },
  css: {
    preprocessorOptions: {
      scss: {
        additionalData: `@use "@/styles/media" as *;`,
      },
    },
  },
});
```

## Рекомендации

1. **Всегда используйте алиасы** вместо относительных путей для импортов из других папок
2. **Для локальных файлов** в той же папке можно использовать относительные пути (`./Component`)
3. **В SCSS** используйте `@/styles/` для импорта общих стилей
4. **Barrel exports** из `@/components` для импорта компонентов

## Миграция существующих файлов

Для обновления существующих файлов замените:
- `../../utils/` → `@/utils/`
- `../../components/` → `@/components/`
- `../../hooks/` → `@/hooks/`
- `../../store/` → `@/store/`
- `../../assets/` → `@/assets/`
- И так далее для других папок
