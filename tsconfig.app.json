{
  "compilerOptions": {
    "tsBuildInfoFile": "./node_modules/.tmp/tsconfig.app.tsbuildinfo",
    "target": "ES2022",
    "useDefineForClassFields": true,
    "lib": ["ES2022", "DOM", "DOM.Iterable"],
    "module": "ESNext",
    "skipLibCheck": true,

    /* Bundler mode */
    "moduleResolution": "bundler",
    "allowImportingTsExtensions": true,
    "verbatimModuleSyntax": true,
    "moduleDetection": "force",
    "noEmit": true,
    "jsx": "react-jsx",

    /* Path mapping */
    "baseUrl": ".",
    "paths": {
      "@/*": ["src/*"],
      "@/components": ["src/components"],
      "@/components/*": ["src/components/*"],
      "@/pages": ["src/pages"],
      "@/pages/*": ["src/pages/*"],
      "@/modules": ["src/modules"],
      "@/modules/*": ["src/modules/*"],
      "@/utils": ["src/utils"],
      "@/utils/*": ["src/utils/*"],
      "@/hooks": ["src/hooks"],
      "@/hooks/*": ["src/hooks/*"],
      "@/store": ["src/store"],
      "@/store/*": ["src/store/*"],
      "@/types": ["src/types"],
      "@/types/*": ["src/types/*"],
      "@/assets": ["src/assets"],
      "@/assets/*": ["src/assets/*"],
      "@/styles": ["src/styles"],
      "@/styles/*": ["src/styles/*"],
      "@/api": ["src/api"],
      "@/api/*": ["src/api/*"],
      "@/contexts": ["src/contexts"],
      "@/contexts/*": ["src/contexts/*"],
      "@/app": ["src/app"],
      "@/app/*": ["src/app/*"]
    },

    /* Linting */
    "strict": true,
    "noUnusedLocals": true,
    "noUnusedParameters": true,
    "erasableSyntaxOnly": false,
    "noFallthroughCasesInSwitch": true,
    "noUncheckedSideEffectImports": true,
    "allowSyntheticDefaultImports": true
  },
  "include": ["src"]
}
