# 🚀 Quick Start: Path Aliases

## Что изменилось?

Теперь вместо ужасных импортов типа:
```typescript
import { HEADER_MENU_ITEMS } from '../../utils/constants';
import { AppButton } from '../../../../components/AppButton';
import AppLogo from '../../../assets/images/Logo.png';
```

Можно писать красиво:
```typescript
import { HEADER_MENU_ITEMS } from '@/utils/constants';
import { AppButton } from '@/components';
import AppLogo from '@/assets/images/Logo.png';
```

## 📋 Шпаргалка по алиасам

| Старый путь | Новый алиас |
|-------------|-------------|
| `../../utils/` | `@/utils/` |
| `../../../components/` | `@/components/` |
| `../../hooks/` | `@/hooks/` |
| `../../store/` | `@/store/` |
| `../../assets/` | `@/assets/` |
| `../../types/` | `@/types/` |
| `../../styles/` | `@/styles/` |

## 🔧 Для SCSS файлов

```scss
// Старый способ
@use '../../styles/media' as *;

// Новый способ
@use '@/styles/media' as *;
```

## 🛠 Автоматическая миграция

Для миграции существующих файлов запустите:
```bash
node scripts/migrate-imports.js
```

## ✅ Что уже обновлено

- ✅ `tsconfig.app.json` - настроены path mappings
- ✅ `vite.config.ts` - настроены алиасы для Vite и SCSS
- ✅ `src/components/index.ts` - добавлены все barrel exports
- ✅ Примеры файлов обновлены для демонстрации

## 📝 Правила использования

1. **Всегда используйте алиасы** для импортов из других папок
2. **Для компонентов** используйте barrel export: `from '@/components'`
3. **Для локальных файлов** в той же папке используйте `./`
4. **В SCSS** всегда используйте `@/styles/` для общих стилей

## 🎯 Преимущества

- 📖 **Читаемость**: Понятно, откуда импорт
- 🔧 **Рефакторинг**: Легко перемещать файлы
- 🚀 **Автодополнение**: IDE лучше работает
- 🎨 **Консистентность**: Единый стиль во всем проекте

Подробная документация в файле `PATH_ALIASES.md`
