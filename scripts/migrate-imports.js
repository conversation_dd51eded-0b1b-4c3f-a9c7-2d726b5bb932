#!/usr/bin/env node

/**
 * <PERSON><PERSON><PERSON> to migrate relative imports to path aliases
 * Usage: node scripts/migrate-imports.js
 */

const fs = require('fs');
const path = require('path');

// Mapping of relative paths to aliases
const pathMappings = {
  // TypeScript/JavaScript imports
  "from '../../utils/": "from '@/utils/",
  "from '../../../utils/": "from '@/utils/",
  "from '../../../../utils/": "from '@/utils/",
  "from '../../components/": "from '@/components/",
  "from '../../../components/": "from '@/components/",
  "from '../../../../components/": "from '@/components/",
  "from '../../hooks/": "from '@/hooks/",
  "from '../../../hooks/": "from '@/hooks/",
  "from '../../../../hooks/": "from '@/hooks/",
  "from '../../store/": "from '@/store/",
  "from '../../../store/": "from '@/store/",
  "from '../../../../store/": "from '@/store/",
  "from '../../types/": "from '@/types/",
  "from '../../../types/": "from '@/types/",
  "from '../../../../types/": "from '@/types/",
  "from '../../assets/": "from '@/assets/",
  "from '../../../assets/": "from '@/assets/",
  "from '../../../../assets/": "from '@/assets/",
  "from '../../api/": "from '@/api/",
  "from '../../../api/": "from '@/api/",
  "from '../../../../api/": "from '@/api/",
  "from '../../contexts/": "from '@/contexts/",
  "from '../../../contexts/": "from '@/contexts/",
  "from '../../../../contexts/": "from '@/contexts/",
  "from '../../app/": "from '@/app/",
  "from '../../../app/": "from '@/app/",
  "from '../../../../app/": "from '@/app/",
  
  // SCSS imports
  "@use '../../styles/": "@use '@/styles/",
  "@use '../../../styles/": "@use '@/styles/",
  "@use '../../../../styles/": "@use '@/styles/",
  "@import '../../styles/": "@import '@/styles/",
  "@import '../../../styles/": "@import '@/styles/",
  "@import '../../../../styles/": "@import '@/styles/",
};

function migrateFile(filePath) {
  try {
    let content = fs.readFileSync(filePath, 'utf8');
    let modified = false;

    // Apply all mappings
    for (const [oldPath, newPath] of Object.entries(pathMappings)) {
      if (content.includes(oldPath)) {
        content = content.replaceAll(oldPath, newPath);
        modified = true;
      }
    }

    // Write back if modified
    if (modified) {
      fs.writeFileSync(filePath, content, 'utf8');
      console.log(`✅ Migrated: ${filePath}`);
      return true;
    }
    
    return false;
  } catch (error) {
    console.error(`❌ Error processing ${filePath}:`, error.message);
    return false;
  }
}

function walkDirectory(dir, extensions = ['.ts', '.tsx', '.js', '.jsx', '.scss', '.css']) {
  const files = [];
  
  function walk(currentDir) {
    const items = fs.readdirSync(currentDir);
    
    for (const item of items) {
      const fullPath = path.join(currentDir, item);
      const stat = fs.statSync(fullPath);
      
      if (stat.isDirectory()) {
        // Skip node_modules and other build directories
        if (!['node_modules', 'dist', 'build', '.git'].includes(item)) {
          walk(fullPath);
        }
      } else if (stat.isFile()) {
        const ext = path.extname(item);
        if (extensions.includes(ext)) {
          files.push(fullPath);
        }
      }
    }
  }
  
  walk(dir);
  return files;
}

function main() {
  console.log('🚀 Starting import migration...\n');
  
  const srcDir = path.join(process.cwd(), 'src');
  
  if (!fs.existsSync(srcDir)) {
    console.error('❌ src directory not found!');
    process.exit(1);
  }
  
  const files = walkDirectory(srcDir);
  console.log(`📁 Found ${files.length} files to check\n`);
  
  let migratedCount = 0;
  
  for (const file of files) {
    if (migrateFile(file)) {
      migratedCount++;
    }
  }
  
  console.log(`\n✨ Migration complete!`);
  console.log(`📊 Migrated ${migratedCount} out of ${files.length} files`);
  
  if (migratedCount === 0) {
    console.log('🎉 All files are already using path aliases!');
  }
}

if (require.main === module) {
  main();
}

module.exports = { migrateFile, walkDirectory, pathMappings };
