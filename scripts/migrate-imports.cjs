#!/usr/bin/env node

/**
 * <PERSON>ript to migrate relative imports to path aliases
 * Usage: node scripts/migrate-imports.js
 */

const fs = require('fs');
const path = require('path');

// Function to convert relative paths to @ alias
function convertToAlias(content) {
  // Regex patterns for different import types
  const patterns = [
    // TypeScript/JavaScript imports: from '../../folder/' -> from '@/folder/'
    {
      regex: /from\s+['"](\.\.[\/\\])+([^'"]+)['"]/g,
      replacement: (match, dots, path) => `from '@/${path}'`,
    },
    // TypeScript/JavaScript imports: from '../../folder' -> from '@/folder'
    {
      regex: /from\s+['"](\.\.[\/\\])+([^'"\/\\]+)['"]/g,
      replacement: (match, dots, path) => `from '@/${path}'`,
    },
    // Import statements: import ... from '../../folder'
    {
      regex: /import\s+[^'"]*from\s+['"](\.\.[\/\\])+([^'"]+)['"]/g,
      replacement: (match, dots, path) =>
        match.replace(/['"](\.\.[\/\\])+([^'"]+)['"]/, `'@/${path}'`),
    },
    // SCSS @use statements: @use '../../styles/' -> @use '@/styles/'
    {
      regex: /@use\s+['"](\.\.[\/\\])+([^'"]+)['"]/g,
      replacement: (match, dots, path) => `@use '@/${path}'`,
    },
    // SCSS @import statements: @import '../../styles/' -> @import '@/styles/'
    {
      regex: /@import\s+['"](\.\.[\/\\])+([^'"]+)['"]/g,
      replacement: (match, dots, path) => `@import '@/${path}'`,
    },
    // Dynamic imports: import('../../folder')
    {
      regex: /import\s*\(\s*['"](\.\.[\/\\])+([^'"]+)['"]\s*\)/g,
      replacement: (match, dots, path) => `import('@/${path}')`,
    },
    // Require statements: require('../../folder')
    {
      regex: /require\s*\(\s*['"](\.\.[\/\\])+([^'"]+)['"]\s*\)/g,
      replacement: (match, dots, path) => `require('@/${path}')`,
    },
  ];

  let modified = false;
  let result = content;

  patterns.forEach((pattern) => {
    const originalResult = result;
    result = result.replace(pattern.regex, pattern.replacement);
    if (result !== originalResult) {
      modified = true;
    }
  });

  return { content: result, modified };
}

function migrateFile(filePath) {
  try {
    const originalContent = fs.readFileSync(filePath, 'utf8');
    const { content, modified } = convertToAlias(originalContent);

    // Write back if modified
    if (modified) {
      fs.writeFileSync(filePath, content, 'utf8');
      console.log(`✅ Migrated: ${filePath}`);
      return true;
    }

    return false;
  } catch (error) {
    console.error(`❌ Error processing ${filePath}:`, error.message);
    return false;
  }
}

function walkDirectory(dir, extensions = ['.ts', '.tsx', '.js', '.jsx', '.scss', '.css']) {
  const files = [];

  function walk(currentDir) {
    const items = fs.readdirSync(currentDir);

    for (const item of items) {
      const fullPath = path.join(currentDir, item);
      const stat = fs.statSync(fullPath);

      if (stat.isDirectory()) {
        // Skip node_modules and other build directories
        if (!['node_modules', 'dist', 'build', '.git'].includes(item)) {
          walk(fullPath);
        }
      } else if (stat.isFile()) {
        const ext = path.extname(item);
        if (extensions.includes(ext)) {
          files.push(fullPath);
        }
      }
    }
  }

  walk(dir);
  return files;
}

function main() {
  console.log('🚀 Starting import migration...\n');

  const srcDir = path.join(process.cwd(), 'src');

  if (!fs.existsSync(srcDir)) {
    console.error('❌ src directory not found!');
    process.exit(1);
  }

  const files = walkDirectory(srcDir);
  console.log(`📁 Found ${files.length} files to check\n`);

  let migratedCount = 0;

  for (const file of files) {
    if (migrateFile(file)) {
      migratedCount++;
    }
  }

  console.log(`\n✨ Migration complete!`);
  console.log(`📊 Migrated ${migratedCount} out of ${files.length} files`);

  if (migratedCount === 0) {
    console.log('🎉 All files are already using path aliases!');
  }
}

if (require.main === module) {
  main();
}

module.exports = { migrateFile, walkDirectory, convertToAlias };
