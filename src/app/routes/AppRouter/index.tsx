import { BrowserRouter, Navigate, Route, Routes } from 'react-router-dom';
import { ROUTER_PATHS } from '@/utils/constants';
import { MainLayout, AdminLayout } from '@/layouts';
import { Sports } from '@/pages/Sports';
import { Settings } from '@/pages/Settings';
import { Profile } from '@/pages/Profile';
import { Wallet } from '@/pages/Wallet';
import { Transactions } from '@/pages/Transactions';
import { FaqHelp } from '@/pages/FaqHelp';
import { LegalPages } from '@/pages/LegalPages';
import { UrlParametersHandler } from '@/utils';
import { AgeConfirmationModal, ResetPasswordModal } from '@/components';

export const AppRouter = () => {
  return (
    <BrowserRouter
      future={{
        v7_startTransition: true,
        v7_relativeSplatPath: false,
      }}
    >
      <UrlParametersHandler />
      <Routes>
        <Route path={ROUTER_PATHS.HOME} element={<Navigate to={ROUTER_PATHS.SPORTS} replace />} />

        {/* Main layout for sports, bets, casino */}
        <Route element={<MainLayout />}>
          <Route path={ROUTER_PATHS.SPORTS} element={<Sports />} />
          <Route path={ROUTER_PATHS.BETS} element={<div>bets</div>} />
          <Route path={ROUTER_PATHS.CASINO} element={<div>casino</div>} />
        </Route>

        {/* Admin layout for user account pages */}
        <Route element={<AdminLayout />}>
          <Route path={ROUTER_PATHS.SETTINGS} element={<Settings />} />
          <Route path={ROUTER_PATHS.PROFILE} element={<Profile />} />
          <Route path={ROUTER_PATHS.WALLET} element={<Wallet />} />
          <Route path={ROUTER_PATHS.TRANSACTIONS} element={<Transactions />} />
          <Route path={ROUTER_PATHS.FAQ_HELP} element={<FaqHelp />} />
          <Route path={ROUTER_PATHS.LEGAL_PAGES} element={<LegalPages />} />
        </Route>

        <Route path={ROUTER_PATHS.NOT_FOUND} element={<div>not-found</div>} />
        <Route path={ROUTER_PATHS.NO_MATCH} element={<div>not-found</div>} />
      </Routes>

      <AgeConfirmationModal />
      <ResetPasswordModal />
    </BrowserRouter>
  );
};
