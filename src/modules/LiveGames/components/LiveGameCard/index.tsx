import type { FC, RefObject } from 'react';
import type { LiveGameT } from '@/index.tsx';
import { SPORT_ICON_MAP } from '@/utils/constants.ts';

import styles from './index.module.scss';
import type { SportType } from '@/types/entities.ts';
import clsx from 'clsx';

type Props = LiveGameT & {
  onClickCard: (newSportType: SportType) => void;
  isActive?: boolean;
  refs: RefObject<Record<string, HTMLButtonElement | null>>;
};

export const LiveGameCard: FC<Props> = ({
  sportType,
  liveGamesCount,
  onClickCard,
  isActive,
  refs,
  id,
}) => {
  const imageSrc = SPORT_ICON_MAP[sportType];

  return (
    <button
      onClick={() => onClickCard(sportType)}
      className={clsx(styles.card, { [styles.cardActive]: isActive })}
      ref={(el) => {
        if (refs.current) refs.current[id] = el;
      }}
    >
      <img
        src={imageSrc}
        alt={sportType}
        className={clsx(styles.image, { [styles.imageActive]: isActive })}
      />
      {isActive && <span className={styles.label}>{sportType}</span>}
      <span className={clsx(styles.counter, { [styles.counterActive]: isActive })}>
        {liveGamesCount}
      </span>
    </button>
  );
};
