import styles from './index.module.scss';
import { useState } from 'react';
import { COEF_DIRECTION, type GameLeague, ODD_TYPE } from '../../types/entities.ts';

import teamOneMini from '../../assets/mocks/team-1-mini.png';
import teamTwoMini from '../../assets/mocks/team-2-mini.png';
import teamThree<PERSON>ini from '../../assets/mocks/team-3-mini.png';
import teamFourMini from '../../assets/mocks/team-4-mini.png';
import teamFiveMini from '../../assets/mocks/team-5-mini.png';
import teamSixMini from '../../assets/mocks/team-6-mini.png';
import { useResponsive } from '../../hooks';
import { AppDropdown, GameLeagueSection } from '../../components';
import { GAMES_SORTING_ORDER_OPTIONS } from '../../utils/constants.ts';

import UpcomingGamesIcon from '../../assets/icons/upcoming-games.svg?react';
import type { RootState } from '../../store';
import { useSelector } from 'react-redux';
import { useGroupedOdds } from '../../hooks/useGroupedOdds.ts';

// eslint-disable-next-line react-refresh/only-export-components
export const mockLiveLeagues: GameLeague = {
  id: 'league-uefa-1',
  leagueName: 'UEFA Champions League',
  gameCount: 0,
  games: [
    {
      id: 'game-1',
      isLive: false,
      tournament: 'UEFA Champions League',
      stage: '1st set',
      startDate: new Date().toISOString(),
      teamOne: {
        name: 'Juventus',
        logo: teamOneMini,
        score: 1,
      },
      teamTwo: {
        name: 'Valley Knights',
        logo: teamTwoMini,
        score: 2,
      },
      odds: [
        {
          value: 0.62,
          direction: COEF_DIRECTION.up,
          isLocked: false,
          oddType: ODD_TYPE.ONE,
          id: 1,
        },
        {
          value: 0.85,
          direction: COEF_DIRECTION.none,
          isLocked: false,
          oddType: ODD_TYPE.DRAW,
          id: 2,
        },
        { value: 0, direction: COEF_DIRECTION.none, isLocked: true, oddType: ODD_TYPE.TWO, id: 3 },
        {
          value: 2.03,
          direction: COEF_DIRECTION.up,
          isLocked: false,
          oddType: ODD_TYPE.OVER,
          id: 4,
        },
        {
          value: 0.35,
          direction: COEF_DIRECTION.down,
          isLocked: false,
          oddType: ODD_TYPE.TOTAL,
          id: 5,
        },
        {
          value: 0,
          direction: COEF_DIRECTION.none,
          isLocked: true,
          oddType: ODD_TYPE.UNDER,
          id: 6,
        },
      ],
      hasMoreMarkets: true,
    },
    {
      id: 'game-2',
      isLive: false,
      tournament: 'UEFA Champions League',
      stage: '1st set',
      startDate: new Date().toISOString(),
      teamOne: {
        name: 'Paris Saint-Germain',
        logo: teamThreeMini,
        score: 5,
      },
      teamTwo: {
        name: 'Cypress Eagles',
        logo: teamFourMini,
        score: 0,
      },
      odds: [
        {
          value: 0.25,
          direction: COEF_DIRECTION.up,
          isLocked: false,
          oddType: ODD_TYPE.ONE,
          id: 1,
        },
        {
          value: 0.7,
          direction: COEF_DIRECTION.down,
          isLocked: false,
          oddType: ODD_TYPE.DRAW,
          id: 2,
        },
        {
          value: 0.21,
          direction: COEF_DIRECTION.down,
          isLocked: false,
          oddType: ODD_TYPE.TWO,
          id: 3,
        },
        {
          value: 1.49,
          direction: COEF_DIRECTION.up,
          isLocked: false,
          oddType: ODD_TYPE.OVER,
          id: 4,
        },
        {
          value: 0.89,
          direction: COEF_DIRECTION.none,
          isLocked: false,
          oddType: ODD_TYPE.TOTAL,
          id: 5,
        },
        {
          value: 0,
          direction: COEF_DIRECTION.none,
          isLocked: true,
          oddType: ODD_TYPE.UNDER,
          id: 6,
        },
      ],
      hasMoreMarkets: true,
    },
    {
      id: 'game-3',
      isLive: false,
      tournament: 'UEFA Champions League',
      stage: '1st set',
      startDate: new Date().toISOString(),
      teamOne: {
        name: 'Inter Milan',
        logo: teamFiveMini,
        score: 0,
      },
      teamTwo: {
        name: 'Horizon Titans',
        logo: teamSixMini,
        score: 4,
      },
      odds: [
        {
          value: 0.57,
          direction: COEF_DIRECTION.none,
          isLocked: false,
          oddType: ODD_TYPE.ONE,
          id: 1,
        },
        {
          value: 0.34,
          direction: COEF_DIRECTION.down,
          isLocked: false,
          oddType: ODD_TYPE.DRAW,
          id: 2,
        },
        {
          value: 0.12,
          direction: COEF_DIRECTION.down,
          isLocked: false,
          oddType: ODD_TYPE.TWO,
          id: 3,
        },
        {
          value: 1.76,
          direction: COEF_DIRECTION.up,
          isLocked: false,
          oddType: ODD_TYPE.OVER,
          id: 4,
        },
        {
          value: 0.67,
          direction: COEF_DIRECTION.up,
          isLocked: false,
          oddType: ODD_TYPE.TOTAL,
          id: 5,
        },
        {
          value: 0,
          direction: COEF_DIRECTION.none,
          isLocked: true,
          oddType: ODD_TYPE.UNDER,
          id: 6,
        },
      ],
      hasMoreMarkets: true,
    },
  ],
};

export const UpcomingGames = () => {
  const [selectedOddGroup, setSelectedOddGroup] = useState<string>('1x2');

  const [selectedOrder, setSelectedOrder] = useState<string>('Date');

  const { isMobile, isTablet } = useResponsive();

  const activeSportType = useSelector((state: RootState) => state.sports.activeSportType);

  const { supportsGroupedOdds, dropdownOptions } = useGroupedOdds({
    activeSportType,
    isMobile,
    isTablet,
  });

  return (
    <div className={styles.wrapper}>
      <div className={styles.topHeader}>
        <div className={styles.headerTopLeft}>
          <UpcomingGamesIcon />
          <h2 className={styles.title}>Upcoming</h2>
        </div>

        {supportsGroupedOdds && isTablet && (
          <div className={styles.headerMiddleTablet}>
            <AppDropdown
              options={dropdownOptions}
              selected={selectedOddGroup}
              onChange={(value) => setSelectedOddGroup(value)}
            />

            <AppDropdown
              label="Order by"
              options={GAMES_SORTING_ORDER_OPTIONS}
              selected={selectedOrder}
              onChange={(value) => setSelectedOrder(value)}
            />
          </div>
        )}
      </div>

      {isMobile && (
        <div className={styles.headerMiddleMobile}>
          <AppDropdown
            options={dropdownOptions}
            selected={selectedOddGroup}
            onChange={(value) => setSelectedOddGroup(value)}
          />

          <AppDropdown
            label="Order by"
            options={GAMES_SORTING_ORDER_OPTIONS}
            selected={selectedOrder}
            onChange={(value) => setSelectedOrder(value)}
          />
        </div>
      )}

      <div>
        <GameLeagueSection
          {...mockLiveLeagues}
          sportType={activeSportType}
          isMobile={isMobile}
          isTablet={isTablet}
          selectedOddGroup={selectedOddGroup}
          supportsGroupedOdds={supportsGroupedOdds}
        />
      </div>
    </div>
  );
};
