import { Tabs } from 'antd';
import { useState } from 'react';
import { PersonalInfo } from './PersonalInfo';
import { NotificationSettings } from './NotificationSettings';
import { Breadcrumbs } from '../../components';
import { SETTINGS_TABS } from '../../utils/constants';
import styles from './index.module.scss';

export const Settings = () => {
  const [activeTab, setActiveTab] = useState('personal-info');

  const getTabContent = (key: string) => {
    switch (key) {
      case 'personal-info':
        return <PersonalInfo />;
      case 'verification':
        return <div>Verification content</div>;
      case 'games-limit':
        return <div>Games Limit content</div>;
      case 'multiple-logins':
        return <div>Multiple Logins content</div>;
      case 'account-deactivation':
        return <div>Account Deactivation content</div>;
      case 'notification-settings':
        return <NotificationSettings />;
      default:
        return <PersonalInfo />;
    }
  };

  const tabItems = SETTINGS_TABS.map((tab) => ({
    key: tab.key,
    label: tab.label,
    children: getTabContent(tab.key),
  }));

  return (
    <div className={styles.settingsPage}>
      <Breadcrumbs />
      <div className={styles.container}>
        <div className={styles.header}>
          <h3 className="text-semibold">Settings</h3>
        </div>

        <div className={styles.content}>
          <Tabs
            activeKey={activeTab}
            onChange={setActiveTab}
            items={tabItems}
            className={styles.settingsTabs}
            tabPosition="top"
          />
        </div>
      </div>
    </div>
  );
};
