import type { <PERSON> } from 'react';
import { AppButton } from '@/AppButton';

import BellIcon from '@/assets/icons/bell.svg?react';

import styles from './index.module.scss';

export const NotificationButton: FC = () => {
  const hasNotification = true;

  return (
    <AppButton variant="default" className={styles.appButton}>
      <BellIcon />
      {hasNotification && <div className={styles.round} />}
    </AppButton>
  );
};
