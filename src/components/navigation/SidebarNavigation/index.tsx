import { Menu } from 'antd';
import { Link } from 'react-router-dom';
import { SIDEBAR_NAV_MENU_ITEMS } from '../../../utils/constants';
import styles from './index.module.scss';

export const SidebarNavigation = () => {
  const sidebarNavMenuItems = SIDEBAR_NAV_MENU_ITEMS.map((item) => ({
    key: item.key,
    icon: <item.icon />,
    label: <Link to={item.path}>{item.label}</Link>,
  }));

  return (
    <div className={styles.container}>
      <h3 className={styles.navTitle}>Navigation</h3>
      <Menu
        mode="vertical"
        items={sidebarNavMenuItems}
        theme="dark"
        defaultSelectedKeys={['sports']}
        className={styles.sidebarNavMenu}
      />
    </div>
  );
};
