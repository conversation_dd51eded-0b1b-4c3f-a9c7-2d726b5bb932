import { type FC, useMemo } from 'react';
import styles from './index.module.scss';
import { type GameEventT, ODD_TYPE } from '@/types/entities.ts';
import dayjs from 'dayjs';
import clsx from 'clsx';

import ChevronRightSmallIcon from '@/assets/icons/chevron-right-small.svg?react';
import StarOutlinedIcon from '@/assets/icons/star-outlined.svg?react';

import advancedFormat from 'dayjs/plugin/advancedFormat';
import { BetOdd } from '@/BetOdd';
import { ODD_GROUPS_MAP } from '@/utils/constants.ts';
import { GameRowHeader } from '@/GameRowHeader';
dayjs.extend(advancedFormat);

type Props = {
  game: GameEventT;
  isMobile: boolean;
  isTablet: boolean;
  supportsGroupedOdds: boolean;
  selectedOddGroup: string;
};

export const GameRow: FC<Props> = ({
  game,
  selectedOddGroup,
  supportsGroupedOdds,
  isMobile,
  isTablet,
}) => {
  const { stage, isLive, startDate, teamOne, teamTwo } = game;

  const oddsToShow = useMemo(() => {
    if (!supportsGroupedOdds) return game.odds;
    if (!isMobile && !isTablet) return game.odds;

    const group: ODD_TYPE[] = ODD_GROUPS_MAP[selectedOddGroup] ?? [];

    return game.odds.filter((odd) => odd.oddType !== undefined && group.includes(odd.oddType));
  }, [game.odds, isMobile, isTablet, selectedOddGroup, supportsGroupedOdds]);

  return (
    <div className={styles.gameContainer}>
      <GameRowHeader isMobile={isMobile} isLive={isLive} stage={stage} startDate={startDate} />

      <div className={styles.gameContent}>
        <div className={styles.gameLeftContent}>
          <div className={styles.teamRow}>
            <StarOutlinedIcon
              className={clsx(styles.startIcon, { [styles.iconHidden]: isMobile })}
            />
            <img src={teamOne.logo} alt={teamOne.name} />

            <span
              className={clsx(styles.teamName, {
                [styles.teamNameHighlighted]: teamOne.score > teamTwo.score,
              })}
            >
              {teamOne.name}
            </span>
          </div>
          <div className={styles.teamRow}>
            <div className={clsx(styles.startIcon, styles.emptySpacer)} />

            <img src={teamTwo.logo} alt={teamTwo.name} />

            <span
              className={clsx(styles.teamName, {
                [styles.teamNameHighlighted]: teamTwo.score > teamOne.score,
              })}
            >
              {teamTwo.name}
            </span>
          </div>
        </div>

        <div className={styles.selectedOddType}>{selectedOddGroup}</div>

        <div className={styles.rightContent}>
          {isLive && (
            <div className={styles.scoreContainer}>
              <div className={styles.scoreColumn}>
                <span className={styles.scoreText}>0</span>
                <span className={styles.scoreText}>0</span>
              </div>

              <div />

              <div className={styles.scoreColumn}>
                <span className={styles.scoreText}>1</span>
                <span className={styles.scoreText}>0</span>
              </div>
            </div>
          )}

          <div className={styles.oddsContainer}>
            {oddsToShow.map((odd) => (
              <BetOdd
                key={odd.id}
                value={odd.value}
                direction={odd.direction}
                isLocked={odd.isLocked}
              />
            ))}
          </div>

          <button className={styles.moreButton}>
            {!isMobile && <span>More</span>}
            <ChevronRightSmallIcon />
          </button>
        </div>
      </div>
    </div>
  );
};
