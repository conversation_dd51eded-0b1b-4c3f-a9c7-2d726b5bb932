import styles from './index.module.scss';
import { LuArrowRight, LuArrowLeft } from 'react-icons/lu';
import { AppButton } from '@/AppButton';

interface RegisterModalButtonsProps {
  currentStep: number;
  totalSteps: number;
  isStepValid: boolean;
  isRegisterLoading: boolean;
  onNext: () => void;
  onBack: () => void;
}

export const RegisterModalButtons = ({
  currentStep,
  totalSteps,
  isStepValid,
  isRegisterLoading,
  onNext,
  onBack,
}: RegisterModalButtonsProps) => {
  const isFirstStep = currentStep === 1;
  const isLastStep = currentStep === totalSteps;

  const renderPrimaryButton = () => {
    if (isLastStep) {
      return (
        <AppButton
          variant="primary"
          key="submit"
          htmlType="submit"
          loading={isRegisterLoading}
          disabled={!isStepValid}
          className={styles.submitButton}
        >
          {isRegisterLoading ? 'Creating account...' : 'Create account'}
        </AppButton>
      );
    }

    return (
      <AppButton
        variant="primary"
        key="next"
        onClick={onNext}
        className={styles.nextButton}
        disabled={!isStepValid}
        block={isFirstStep}
      >
        Next
        <LuArrowRight size={20} />
      </AppButton>
    );
  };

  const renderBackButton = () => (
    <AppButton variant="default" key="back" onClick={onBack} disabled={isRegisterLoading}>
      <LuArrowLeft size={20} /> Back
    </AppButton>
  );

  if (isFirstStep) {
    return <div className={styles.buttons}>{renderPrimaryButton()}</div>;
  }

  return (
    <div className={styles.buttons}>
      {renderBackButton()}
      {renderPrimaryButton()}
    </div>
  );
};
