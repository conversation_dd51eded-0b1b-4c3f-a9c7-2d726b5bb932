import { type FC } from 'react';
import { Menu } from 'antd';
import { useDispatch, useSelector } from 'react-redux';
import { type RootState, type AppDispatch } from '../../store';
import { openLoginModal, openRegisterModal } from '../../store/slices/modalSlice';
import { UserProfile, LoginModal, RegisterModal } from '../../components';
import styles from './index.module.scss';

import AppLogo from '../../assets/images/Logo.png';

import { AppButton, LanguagePicker, WalletButton, NotificationButton } from '../../components';
import { useResponsive } from '../../hooks';
import { HEADER_MENU_ITEMS } from '../../utils/constants';

export const Header: FC = () => {
  const { isDesktop, isLaptop, isMobile } = useResponsive();
  const dispatch = useDispatch<AppDispatch>();
  const { user } = useSelector((state: RootState) => state.auth);

  const handleOpenLogin = () => {
    dispatch(openLoginModal());
  };

  const handleOpenRegister = () => {
    dispatch(openRegisterModal());
  };

  const headerMenuItems = HEADER_MENU_ITEMS.map((item) => ({
    key: item.key,
    icon: <item.icon />,
    label: item.label,
  }));

  return (
    <>
      <header className={styles.container}>
        {isDesktop || isLaptop ? (
          <Menu
            mode="horizontal"
            items={headerMenuItems}
            theme="dark"
            defaultSelectedKeys={['sports']}
            className={styles.topNavMenu}
          />
        ) : (
          <img src={AppLogo} alt="appLogo" className={styles.appLogo} />
        )}

        <div className={styles.authSection}>
          {user ? (
            <div className={styles.row}>
              {!isMobile && (
                <div className={styles.leftSection}>
                  <LanguagePicker />
                  <NotificationButton />
                  <WalletButton />
                </div>
              )}

              <UserProfile />
            </div>
          ) : (
            <>
              {!isMobile && <LanguagePicker />}
              <div className={styles.authButtons}>
                <AppButton variant="outlined" onClick={handleOpenLogin}>
                  Login
                </AppButton>
                <AppButton variant="primary" onClick={handleOpenRegister}>
                  Register
                </AppButton>
              </div>
            </>
          )}
        </div>
      </header>

      <LoginModal />
      <RegisterModal />
    </>
  );
};
